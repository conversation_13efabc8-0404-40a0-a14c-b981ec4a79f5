import React from "react";
import { renderHook } from "@testing-library/react";
import { useKeyframes } from "@/components/editor/planck-version/hooks/use-keyframes";
import { KeyframeProvider } from "@/components/editor/planck-version/contexts/keyframe-context";
import { OverlayType, ClipOverlay } from "@/components/editor/planck-version/types";

// Mock the constants
jest.mock("@/components/editor/planck-version/constants", () => ({
  DISABLE_VIDEO_KEYFRAMES: false,
  FPS: 30,
}));

describe("useKeyframes - Split Overlay Fix", () => {
  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <KeyframeProvider>{children}</KeyframeProvider>
  );

  // Mock video overlay - original
  const originalVideoOverlay: ClipOverlay = {
    id: 1,
    type: OverlayType.VIDEO,
    from: 0,
    row: 0,
    durationInFrames: 300, // 10 seconds at 30fps
    videoStartTime: 0, // Starts from beginning of video
    height: 720,
    width: 1280,
    left: 0,
    top: 0,
    rotation: 0,
    isDragging: false,
    content: "Original Video",
    src: "test-video.mp4",
    styles: {
      opacity: 1,
      zIndex: 1,
      objectFit: "cover",
    },
  };

  // Mock video overlay - split second half
  const splitVideoOverlay: ClipOverlay = {
    ...originalVideoOverlay,
    id: 2, // New ID for split overlay
    from: 150, // Starts at frame 150 in timeline
    durationInFrames: 150, // 5 seconds duration
    videoStartTime: 150, // Should start from frame 150 of original video (5 seconds)
    content: "Split Video - Second Half",
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should generate different cache keys for original and split overlays", () => {
    const containerRef = React.createRef<HTMLDivElement>();
    
    // Render hook for original overlay
    const { result: originalResult } = renderHook(
      () => useKeyframes({
        overlay: originalVideoOverlay,
        containerRef,
        currentFrame: 0,
        zoomScale: 1,
      }),
      { wrapper }
    );

    // Render hook for split overlay
    const { result: splitResult } = renderHook(
      () => useKeyframes({
        overlay: splitVideoOverlay,
        containerRef,
        currentFrame: 0,
        zoomScale: 1,
      }),
      { wrapper }
    );

    // Both hooks should be initialized (this test mainly verifies no errors occur)
    expect(originalResult.current).toBeDefined();
    expect(splitResult.current).toBeDefined();
  });

  it("should include videoStartTime in overlay metadata", () => {
    const containerRef = React.createRef<HTMLDivElement>();
    
    const { result } = renderHook(
      () => useKeyframes({
        overlay: splitVideoOverlay,
        containerRef,
        currentFrame: 0,
        zoomScale: 1,
      }),
      { wrapper }
    );

    // The hook should process the overlay with videoStartTime correctly
    expect(result.current).toBeDefined();
    expect(result.current.isLoading).toBeDefined();
  });

  it("should handle overlays with different videoStartTime values", () => {
    const containerRef = React.createRef<HTMLDivElement>();
    
    // Create multiple split overlays with different start times
    const firstSplit: ClipOverlay = {
      ...originalVideoOverlay,
      id: 3,
      videoStartTime: 0,
    };

    const secondSplit: ClipOverlay = {
      ...originalVideoOverlay,
      id: 4,
      videoStartTime: 100,
    };

    const thirdSplit: ClipOverlay = {
      ...originalVideoOverlay,
      id: 5,
      videoStartTime: 200,
    };

    // Render hooks for all splits
    const { result: firstResult } = renderHook(
      () => useKeyframes({
        overlay: firstSplit,
        containerRef,
        currentFrame: 0,
        zoomScale: 1,
      }),
      { wrapper }
    );

    const { result: secondResult } = renderHook(
      () => useKeyframes({
        overlay: secondSplit,
        containerRef,
        currentFrame: 0,
        zoomScale: 1,
      }),
      { wrapper }
    );

    const { result: thirdResult } = renderHook(
      () => useKeyframes({
        overlay: thirdSplit,
        containerRef,
        currentFrame: 0,
        zoomScale: 1,
      }),
      { wrapper }
    );

    // All hooks should be initialized without errors
    expect(firstResult.current).toBeDefined();
    expect(secondResult.current).toBeDefined();
    expect(thirdResult.current).toBeDefined();
  });

  it("should handle overlays without videoStartTime (backward compatibility)", () => {
    const containerRef = React.createRef<HTMLDivElement>();
    
    const overlayWithoutStartTime: ClipOverlay = {
      ...originalVideoOverlay,
      videoStartTime: undefined,
    };

    const { result } = renderHook(
      () => useKeyframes({
        overlay: overlayWithoutStartTime,
        containerRef,
        currentFrame: 0,
        zoomScale: 1,
      }),
      { wrapper }
    );

    // Should handle undefined videoStartTime gracefully
    expect(result.current).toBeDefined();
    expect(result.current.isLoading).toBeDefined();
  });
});
