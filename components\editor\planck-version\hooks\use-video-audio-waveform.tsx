import { useState, useEffect } from "react";

interface WaveformData {
  peaks: number[];
  length: number;
}

interface VideoAudioOptions {
  numPoints?: number;
  fps?: number;
}

/**
 * A React hook that extracts audio waveform data from video files.
 * Uses the Web Audio API to decode audio from video files and generate waveform visualization data.
 *
 * @param src - URL of the video file to process
 * @param videoStartTime - Start time offset in frames for split videos (default: 0)
 * @param durationInFrames - Duration to process in frames
 * @param options - Configuration options
 * @param options.numPoints - Number of data points to generate for the waveform (default: 100)
 * @param options.fps - Frames per second for time calculations (default: 30)
 *
 * @returns Object containing:
 *   - waveformData: WaveformData object with peaks array and length, or null if not ready
 *   - isLoading: Boolean indicating if processing is in progress
 */
export function useVideoAudioWaveform(
  src: string | undefined,
  videoStartTime: number = 0,
  durationInFrames: number,
  options: VideoAudioOptions = {}
) {
  const [waveformData, setWaveformData] = useState<WaveformData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { numPoints = 100, fps = 30 } = options;

  useEffect(() => {
    if (!src) {
      setWaveformData(null);
      return;
    }

    let isActive = true;

    const processVideoAudio = async () => {
      try {
        setIsLoading(true);
        setWaveformData(null);

        // Fetch the video file as array buffer
        const response = await fetch(src);
        const arrayBuffer = await response.arrayBuffer();
        
        // Create audio context
        const audioContext = new AudioContext();
        
        // Decode audio data from video
        const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);

        if (!isActive) return;

        const sampleRate = audioBuffer.sampleRate;
        const channelData = audioBuffer.getChannelData(0);
        const startTime = videoStartTime / fps;
        const duration = durationInFrames / fps;

        const startSample = Math.floor(startTime * sampleRate);
        const samplesForDuration = Math.floor(duration * sampleRate);
        const samplesPerPeak = Math.max(1, Math.floor(samplesForDuration / numPoints));

        const peaks = Array.from({ length: numPoints }, (_, i) => {
          const start = startSample + i * samplesPerPeak;
          const end = Math.min(start + samplesPerPeak, channelData.length);

          let peakMax = 0;
          let sumSquares = 0;
          let validSamples = 0;

          for (let j = start; j < end; j++) {
            if (j >= channelData.length) break;
            const value = Math.abs(channelData[j]);
            peakMax = Math.max(peakMax, value);
            sumSquares += value * value;
            validSamples++;
          }

          if (validSamples === 0) return 0;
          const rms = Math.sqrt(sumSquares / validSamples);
          return (peakMax + rms) / 2;
        });

        // Normalize using 95th percentile to avoid outliers
        const sortedPeaks = [...peaks].sort((a, b) => a - b);
        const normalizeValue = sortedPeaks[Math.floor(peaks.length * 0.95)] || 1;
        const normalizedPeaks = peaks.map((peak) =>
          Math.min(peak / normalizeValue, 1)
        );

        if (isActive) {
          setWaveformData({
            peaks: normalizedPeaks,
            length: samplesForDuration,
          });
        }
      } catch (error) {
        console.error("Error processing video audio waveform:", error);
        // Create a fallback waveform with subtle random data
        if (isActive) {
          const fallbackPeaks = Array.from({ length: numPoints }, () => 
            Math.random() * 0.2 + 0.05
          );
          setWaveformData({
            peaks: fallbackPeaks,
            length: numPoints,
          });
        }
      } finally {
        if (isActive) {
          setIsLoading(false);
        }
      }
    };

    processVideoAudio();

    return () => {
      isActive = false;
      setIsLoading(false);
    };
  }, [src, videoStartTime, durationInFrames, fps, numPoints]);

  return { waveformData, isLoading };
}
