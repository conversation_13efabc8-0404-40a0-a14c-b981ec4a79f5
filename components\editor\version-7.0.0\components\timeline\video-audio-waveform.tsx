import { memo, useMemo } from "react";

interface WaveformData {
  peaks: number[];
  length: number;
}

interface VideoAudioWaveformProps {
  waveformData: WaveformData;
  totalSlots: number;
}

/**
 * VideoAudioWaveform Component
 *
 * A memoized component that renders an audio waveform visualization for video files.
 * This component is designed to be displayed below video thumbnails in the timeline.
 * 
 * Features:
 * - Compact visualization optimized for timeline display
 * - Responsive to the number of thumbnail slots
 * - Subtle visual design that doesn't interfere with video thumbnails
 *
 * @component
 * @param {VideoAudioWaveformProps} props - Component properties
 * @returns {JSX.Element} Rendered waveform visualization
 */
const VideoAudioWaveform = memo(
  ({ waveformData, totalSlots }: VideoAudioWaveformProps) => {
    // Calculate how many peaks to show based on available space
    const peaksToShow = Math.min(
      waveformData.peaks.length,
      Math.max(20, totalSlots * 8) // Show more peaks for better resolution
    );

    const sampledPeaks = useMemo(
      () =>
        waveformData.peaks.filter(
          (_, index) =>
            index % Math.ceil(waveformData.peaks.length / peaksToShow) === 0
        ),
      [waveformData.peaks, peaksToShow]
    );

    return (
      <div className="absolute bottom-0 left-0 right-0 h-4 flex items-end justify-between px-1">
        {sampledPeaks.map((peak, index) => {
          // Use a more subtle height calculation for video audio
          const height = Math.max(Math.pow(peak, 0.8) * 80, 2);
          return (
            <div
              key={index}
              className="relative flex-1 mx-[0.25px]"
              style={{ height: "100%" }}
            >
              <div
                className="absolute bottom-0 w-full bg-blue-300/60 dark:bg-blue-400/50 rounded-sm"
                style={{
                  height: `${height}%`,
                  minHeight: "2px",
                }}
              />
            </div>
          );
        })}
      </div>
    );
  }
);

VideoAudioWaveform.displayName = "VideoAudioWaveform";

export default VideoAudioWaveform;
