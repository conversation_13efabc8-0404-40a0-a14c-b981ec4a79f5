---
version: "7.0.0"
date: "2025-04-13"
title: "Version 7.0.0"
description: "RVE now works on mobile. Introducing mobile responsiveness, templates, clip speed control, and custom assets."
founderNotes: "This update focuses on closing out some of the most highly requested features from the roadmap. || The biggest change: you can now use the editor on mobile. Mobile responsiveness is built-in and can be toggled via settings. || We’ve also introduced templates and stickers, a major step toward customizable, reusable editing flows. Create your own templates and export them, or explore new animated stickers from the RVE library. || On top of that, you can now upload your own images, videos, and audio locally, control playback rate, and adjust individual clip speeds. || There are also a number of bug fixes and unit tests under the hood. As always, let us know what you think!"
changes:
  - "Added mobile responsive layout with toggleable settings."
  - "Introduced Video Editor Templates: configure, save, and export your own setups."
  - "Image / Video Preset Filters."
  - "Added ability to change individual clip speeds."
  - "Added global playback rate control."
  - "Enabled local uploads of custom assets (images, videos, and sounds - limited to local dev atm)."
  - "Introduced stickers, including animated stickers from the RVE template library."
  - "Various bug fixes and new unit tests for better stability."
image: "https://rwxrdxvxndclnqvznxfj.supabase.co/storage/v1/object/public/rve-version-7//rve-version-7-min.png"
status: "BETA"
branch: "version-7"
---
