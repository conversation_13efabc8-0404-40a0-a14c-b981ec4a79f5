---
version: "4.0.0"
date: "2024-10-04"
title: "Version 4.0.0"
description: "Improved modular components, enhanced overlays, and video rendering capabilities"
founderNotes: "This version was initially focused on enhancing RVE with video rendering capabilities, allowing users to export videos directly and download them. However, looking further into Remotion’s functionality, this evolved into a slight re-architecture, making RVE more modular and adaptable. Now, with a fully component-based structure—including modular hooks and contexts—features like the timeline and timeline items should be easily integrated into your Remotion projects or existing business codebases. There may be some gotchas but we can work our way through that together. || It also introduces an improved overlay system, editable text and clip overlays, and many other features. If you're upgrading to this version from a previous one, you may find it a bit more complex due to the added rendering capabilities of Remotion (as well as the refactoring), which require some understanding and adjustment. I’ve aimed to keep things as straightforward as possible to ensure a smooth setup. || Please note that the mobile-responsive feature has been temporarily removed, but it will be revisited in future versions. If needed, you can refer back to version 3 for insights into this feature. || If you encounter any issues or need assistance, please feel free to reach out to support. I’m here to help make your experience as seamless as possible. Thank you for your continued interest and trust in RVE!"
changes:
  - Complete Code Refactoring with Modular Components, Hooks, and Context API
  - Enhanced Text Overlay System with New Editing Interface
  - New Timeline Implementation
  - Searchable Clips Library
  - Improved UI with Collapsible shadcn/ui Sidebar
  - Advanced Video Processing and Rendering Engine
  - Versioned Rendering API with Aspect Ratio Support
  - Performance and State Management Optimizations
image: "/images/react-video-editor-version-4.png"
status: "Archived"
branch: "-"
---
