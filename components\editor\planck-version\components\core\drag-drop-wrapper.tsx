"use client";

import React, { ReactNode } from "react";
import { useSimpleDragDrop } from "../../hooks/use-simple-drag-drop";
import { Upload } from "lucide-react";

interface DragDropWrapperProps {
  children: ReactNode;
  onFilesAdded: (files: File[]) => void;
  disabled?: boolean;
  className?: string;
}

/**
 * Simple wrapper component that adds global drag & drop functionality
 * to the entire editor interface without disrupting existing layouts
 */
export const DragDropWrapper: React.FC<DragDropWrapperProps> = ({
  children,
  onFilesAdded,
  disabled = false,
  className = "",
}) => {
  const { state, handlers } = useSimpleDragDrop({
    onFilesDropped: onFilesAdded,
    disabled,
  });
  const {
    handleDragEnter: onDragEnter,
    handleDragLeave: onDragLeave,
    handleDragOver: onDragOver,
    handleDrop: onDrop,
  } = handlers;

  return (
    <div
      className={`relative w-full h-full ${className}`}
      onDragEnter={onDragEnter}
      onDragLeave={onDragLeave}
      onDragOver={onDragOver}
      onDrop={onDrop}
    >
      {children}

      {/* Global drag overlay - only shows when dragging files over the editor */}
      {state.isDragOver && (
        <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-blue-500/10 backdrop-blur-sm">
          <div className="text-center p-8 bg-white/95 dark:bg-gray-800/95 rounded-xl shadow-2xl border border-blue-200 dark:border-blue-800 max-w-md mx-4">
            <Upload className="mx-auto h-16 w-16 text-blue-500 mb-4 animate-bounce" />
            <h3 className="text-2xl font-bold text-blue-600 dark:text-blue-400 mb-2">
              Drop files anywhere
            </h3>
            <p className="text-blue-600/80 dark:text-blue-400/80">
              Release to add media to your timeline
            </p>
            <div className="mt-4 flex justify-center gap-6 text-sm text-blue-500/70">
              <span className="flex items-center gap-1">🎬 Video</span>
              <span className="flex items-center gap-1">🎵 Audio</span>
              <span className="flex items-center gap-1">🖼️ Images</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
